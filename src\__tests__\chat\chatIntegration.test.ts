import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { AuthProvider } from '../../lib/auth/AuthContext';
import Chat from '../../components/chat/Chat';
import ChatInput from '../../components/chat/ChatInput';
import ChatMessage from '../../components/chat/ChatMessage';
import { ChatMessage as ChatMessageType } from '../../lib/supabase';

// Mock Supabase
jest.mock('../../lib/supabase', () => ({
  supabase: {
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          order: jest.fn(() => ({
            data: [],
            error: null
          }))
        }))
      })),
      insert: jest.fn(() => ({
        select: jest.fn(() => ({
          single: jest.fn(() => ({
            data: { id: '1', message: 'Test message' },
            error: null
          }))
        }))
      }))
    })),
    channel: jest.fn(() => ({
      on: jest.fn(() => ({
        subscribe: jest.fn()
      })),
      track: jest.fn(),
      untrack: jest.fn()
    })),
    removeChannel: jest.fn()
  },
  db: {
    getChatRooms: jest.fn(() => Promise.resolve([])),
    getChatMessages: jest.fn(() => Promise.resolve([])),
    sendChatMessage: jest.fn(() => Promise.resolve({ id: '1', message: 'Test' })),
    updateLastReadAt: jest.fn(() => Promise.resolve())
  }
}));

// Mock AuthContext
const mockUser = {
  id: 'user-1',
  email: '<EMAIL>'
};

const MockAuthProvider = ({ children }: { children: React.ReactNode }) => (
  <div data-testid="mock-auth-provider">
    {children}
  </div>
);

describe('Chat Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('ChatInput Component', () => {
    it('should render input field and send button', () => {
      const mockOnSend = jest.fn();
      render(<ChatInput onSendMessage={mockOnSend} />);
      
      expect(screen.getByPlaceholderText(/type your message/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /send/i })).toBeInTheDocument();
    });

    it('should call onSendMessage when form is submitted', async () => {
      const mockOnSend = jest.fn();
      const user = userEvent.setup();
      
      render(<ChatInput onSendMessage={mockOnSend} />);
      
      const input = screen.getByPlaceholderText(/type your message/i);
      const sendButton = screen.getByRole('button', { name: /send/i });
      
      await user.type(input, 'Hello world');
      await user.click(sendButton);
      
      expect(mockOnSend).toHaveBeenCalledWith('Hello world');
    });

    it('should clear input after sending message', async () => {
      const mockOnSend = jest.fn();
      const user = userEvent.setup();
      
      render(<ChatInput onSendMessage={mockOnSend} />);
      
      const input = screen.getByPlaceholderText(/type your message/i) as HTMLTextAreaElement;
      const sendButton = screen.getByRole('button', { name: /send/i });
      
      await user.type(input, 'Hello world');
      await user.click(sendButton);
      
      expect(input.value).toBe('');
    });

    it('should not send empty messages', async () => {
      const mockOnSend = jest.fn();
      const user = userEvent.setup();
      
      render(<ChatInput onSendMessage={mockOnSend} />);
      
      const sendButton = screen.getByRole('button', { name: /send/i });
      await user.click(sendButton);
      
      expect(mockOnSend).not.toHaveBeenCalled();
    });

    it('should send message on Enter key press', async () => {
      const mockOnSend = jest.fn();
      const user = userEvent.setup();
      
      render(<ChatInput onSendMessage={mockOnSend} />);
      
      const input = screen.getByPlaceholderText(/type your message/i);
      
      await user.type(input, 'Hello world');
      await user.keyboard('{Enter}');
      
      expect(mockOnSend).toHaveBeenCalledWith('Hello world');
    });

    it('should create new line on Shift+Enter', async () => {
      const mockOnSend = jest.fn();
      const user = userEvent.setup();
      
      render(<ChatInput onSendMessage={mockOnSend} />);
      
      const input = screen.getByPlaceholderText(/type your message/i) as HTMLTextAreaElement;
      
      await user.type(input, 'Line 1');
      await user.keyboard('{Shift>}{Enter}{/Shift}');
      await user.type(input, 'Line 2');
      
      expect(input.value).toContain('\n');
      expect(mockOnSend).not.toHaveBeenCalled();
    });

    it('should call onTyping when user types', async () => {
      const mockOnSend = jest.fn();
      const mockOnTyping = jest.fn();
      const user = userEvent.setup();
      
      render(<ChatInput onSendMessage={mockOnSend} onTyping={mockOnTyping} />);
      
      const input = screen.getByPlaceholderText(/type your message/i);
      await user.type(input, 'H');
      
      expect(mockOnTyping).toHaveBeenCalled();
    });
  });

  describe('ChatMessage Component', () => {
    const mockMessage: ChatMessageType = {
      id: '1',
      room_id: 'room-1',
      sender_id: 'user-1',
      message: 'Hello world',
      message_type: 'text',
      is_edited: false,
      created_at: new Date().toISOString(),
      sender: {
        id: 'user-1',
        email: '<EMAIL>',
        display_name: 'John Doe',
        role: 'user',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    };

    it('should render message content', () => {
      render(<ChatMessage message={mockMessage} isOwn={false} />);
      
      expect(screen.getByText('Hello world')).toBeInTheDocument();
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    it('should show different styling for own messages', () => {
      const { container } = render(<ChatMessage message={mockMessage} isOwn={true} />);
      
      // Own messages should be aligned to the right
      const messageContainer = container.querySelector('[class*="justify-end"]');
      expect(messageContainer).toBeInTheDocument();
    });

    it('should show different styling for received messages', () => {
      const { container } = render(<ChatMessage message={mockMessage} isOwn={false} />);
      
      // Received messages should be aligned to the left
      const messageContainer = container.querySelector('[class*="justify-start"]');
      expect(messageContainer).toBeInTheDocument();
    });

    it('should display timestamp', () => {
      render(<ChatMessage message={mockMessage} isOwn={false} />);
      
      // Should show some form of timestamp (exact format may vary)
      const timeElements = screen.getAllByText(/\d/);
      expect(timeElements.length).toBeGreaterThan(0);
    });

    it('should show edited indicator when message is edited', () => {
      const editedMessage = { ...mockMessage, is_edited: true };
      render(<ChatMessage message={editedMessage} isOwn={false} />);
      
      expect(screen.getByText(/edited/i)).toBeInTheDocument();
    });

    it('should handle system messages differently', () => {
      const systemMessage = {
        ...mockMessage,
        message_type: 'system' as const,
        message: 'User joined the chat'
      };
      
      render(<ChatMessage message={systemMessage} isOwn={false} />);
      
      expect(screen.getByText('User joined the chat')).toBeInTheDocument();
    });

    it('should handle file messages', () => {
      const fileMessage = {
        ...mockMessage,
        message_type: 'file' as const,
        message: 'document.pdf',
        file_url: 'https://example.com/document.pdf'
      };
      
      render(<ChatMessage message={fileMessage} isOwn={false} />);
      
      const link = screen.getByRole('link');
      expect(link).toHaveAttribute('href', 'https://example.com/document.pdf');
    });
  });

  describe('Chat Component Error Handling', () => {
    it('should show loading state', () => {
      // Mock loading state
      jest.doMock('../../lib/auth/AuthContext', () => ({
        useAuth: () => ({ user: null, loading: true })
      }));
      
      render(<Chat />);
      
      // Should show loading spinner or similar
      const loadingElement = screen.getByRole('status') || screen.getByText(/loading/i);
      expect(loadingElement).toBeInTheDocument();
    });

    it('should show authentication required message when not logged in', () => {
      jest.doMock('../../lib/auth/AuthContext', () => ({
        useAuth: () => ({ user: null, loading: false })
      }));
      
      render(<Chat />);
      
      expect(screen.getByText(/authentication required/i)).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      const mockOnSend = jest.fn();
      render(<ChatInput onSendMessage={mockOnSend} />);
      
      const input = screen.getByPlaceholderText(/type your message/i);
      const sendButton = screen.getByRole('button', { name: /send/i });
      
      expect(input).toHaveAttribute('aria-label', expect.any(String));
      expect(sendButton).toHaveAttribute('aria-label', expect.any(String));
    });

    it('should be keyboard navigable', async () => {
      const mockOnSend = jest.fn();
      const user = userEvent.setup();
      
      render(<ChatInput onSendMessage={mockOnSend} />);
      
      // Tab to input
      await user.tab();
      expect(screen.getByPlaceholderText(/type your message/i)).toHaveFocus();
      
      // Tab to send button
      await user.tab();
      expect(screen.getByRole('button', { name: /send/i })).toHaveFocus();
    });
  });
});
