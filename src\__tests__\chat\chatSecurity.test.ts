import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import {
  sanitizeMessageContent,
  validateFile,
  moderateContent,
  RATE_LIMITS
} from '../../lib/security/chatSecurity';

describe('Chat Security', () => {
  describe('sanitizeMessageContent', () => {
    it('should remove HTML tags', () => {
      const input = '<script>alert("xss")</script>Hello World';
      const result = sanitizeMessageContent(input);
      expect(result).toBe('Hello World');
    });

    it('should remove dangerous characters', () => {
      const input = 'Hello <>&\'"World';
      const result = sanitizeMessageContent(input);
      expect(result).toBe('Hello World');
    });

    it('should limit message length', () => {
      const input = 'a'.repeat(1500);
      const result = sanitizeMessageContent(input);
      expect(result.length).toBe(RATE_LIMITS.MAX_MESSAGE_LENGTH);
    });

    it('should handle empty or invalid input', () => {
      expect(sanitizeMessageContent('')).toBe('');
      expect(sanitizeMessageContent(null as any)).toBe('');
      expect(sanitizeMessageContent(undefined as any)).toBe('');
    });

    it('should preserve normal text and unicode', () => {
      const input = 'Hello 👋 World! How are you? 😊';
      const result = sanitizeMessageContent(input);
      expect(result).toBe('Hello 👋 World! How are you? 😊');
    });
  });

  describe('validateFile', () => {
    it('should accept valid image files', () => {
      const file = new File([''], 'test.jpg', { type: 'image/jpeg' });
      Object.defineProperty(file, 'size', { value: 1024 * 1024 }); // 1MB
      
      const result = validateFile(file);
      expect(result.valid).toBe(true);
    });

    it('should reject files that are too large', () => {
      const file = new File([''], 'test.jpg', { type: 'image/jpeg' });
      Object.defineProperty(file, 'size', { value: 15 * 1024 * 1024 }); // 15MB
      
      const result = validateFile(file);
      expect(result.valid).toBe(false);
      expect(result.error).toContain('File size must be less than');
    });

    it('should reject disallowed file types', () => {
      const file = new File([''], 'test.exe', { type: 'application/x-executable' });
      Object.defineProperty(file, 'size', { value: 1024 });
      
      const result = validateFile(file);
      expect(result.valid).toBe(false);
      expect(result.error).toContain('File type not allowed');
    });

    it('should reject suspicious file extensions', () => {
      const file = new File([''], 'malware.exe', { type: 'image/jpeg' });
      Object.defineProperty(file, 'size', { value: 1024 });
      
      const result = validateFile(file);
      expect(result.valid).toBe(false);
      expect(result.error).toContain('File type not allowed for security reasons');
    });

    it('should accept PDF files', () => {
      const file = new File([''], 'document.pdf', { type: 'application/pdf' });
      Object.defineProperty(file, 'size', { value: 1024 * 1024 }); // 1MB
      
      const result = validateFile(file);
      expect(result.valid).toBe(true);
    });
  });

  describe('moderateContent', () => {
    it('should approve clean content', () => {
      const content = 'Hello, how are you today?';
      const result = moderateContent(content);
      
      expect(result.approved).toBe(true);
      expect(result.sanitized).toBe(content);
    });

    it('should reject content with profanity', () => {
      const content = 'This is spam content';
      const result = moderateContent(content);
      
      expect(result.approved).toBe(false);
      expect(result.reason).toContain('inappropriate language');
    });

    it('should reject content with too many links', () => {
      const content = 'Check out https://link1.com and https://link2.com and https://link3.com and https://link4.com';
      const result = moderateContent(content);
      
      expect(result.approved).toBe(false);
      expect(result.reason).toContain('Too many links');
    });

    it('should allow content with few links', () => {
      const content = 'Check out my portfolio at https://mysite.com';
      const result = moderateContent(content);
      
      expect(result.approved).toBe(true);
    });

    it('should sanitize content even when rejecting', () => {
      const content = '<script>spam</script>';
      const result = moderateContent(content);
      
      expect(result.sanitized).toBe('spam');
    });
  });

  describe('RATE_LIMITS', () => {
    it('should have reasonable rate limits', () => {
      expect(RATE_LIMITS.MESSAGES_PER_HOUR).toBeGreaterThan(0);
      expect(RATE_LIMITS.MESSAGES_PER_MINUTE).toBeGreaterThan(0);
      expect(RATE_LIMITS.MAX_MESSAGE_LENGTH).toBeGreaterThan(100);
      expect(RATE_LIMITS.MAX_FILE_SIZE).toBeGreaterThan(1024 * 1024); // At least 1MB
    });

    it('should have allowed file types', () => {
      expect(RATE_LIMITS.ALLOWED_FILE_TYPES).toContain('image/jpeg');
      expect(RATE_LIMITS.ALLOWED_FILE_TYPES).toContain('application/pdf');
      expect(RATE_LIMITS.ALLOWED_FILE_TYPES).toContain('text/plain');
    });
  });
});
