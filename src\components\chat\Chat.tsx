'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChatRoom as ChatRoomType } from '@/lib/supabase';
import { useAuth } from '@/lib/auth/AuthContext';
import ChatList from './ChatList';
import ChatRoom from './ChatRoom';

interface ChatProps {
  className?: string;
}

export default function Chat({ className = '' }: ChatProps) {
  const { user, loading } = useAuth();
  const [selectedRoom, setSelectedRoom] = useState<ChatRoomType | null>(null);
  const [isMobileView, setIsMobileView] = useState(false);

  const handleSelectRoom = (room: ChatRoomType) => {
    setSelectedRoom(room);
    setIsMobileView(true);
  };

  const handleBackToList = () => {
    setSelectedRoom(null);
    setIsMobileView(false);
  };

  if (loading) {
    return (
      <div className={`flex items-center justify-center h-96 ${className}`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className={`flex flex-col items-center justify-center h-96 text-center p-4 ${className}`}>
        <div className="text-6xl mb-4">🔒</div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          Authentication Required
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Please sign in to access the chat system.
        </p>
      </div>
    );
  }

  return (
    <div className={`flex h-full bg-white dark:bg-gray-900 rounded-lg shadow-lg overflow-hidden ${className}`}>
      {/* Mobile Layout */}
      <div className="flex w-full md:hidden">
        <AnimatePresence mode="wait">
          {!isMobileView ? (
            <motion.div
              key="chat-list"
              initial={{ x: -300, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              exit={{ x: -300, opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="w-full"
            >
              <ChatList
                onSelectRoom={handleSelectRoom}
                selectedRoomId={selectedRoom?.id}
              />
            </motion.div>
          ) : selectedRoom ? (
            <motion.div
              key="chat-room"
              initial={{ x: 300, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              exit={{ x: 300, opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="w-full h-full"
            >
              <ChatRoom
                room={selectedRoom}
                onClose={handleBackToList}
              />
            </motion.div>
          ) : null}
        </AnimatePresence>
      </div>

      {/* Desktop Layout */}
      <div className="hidden md:flex w-full">
        {/* Chat List Sidebar */}
        <div className="w-1/3 border-r border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
          <ChatList
            onSelectRoom={handleSelectRoom}
            selectedRoomId={selectedRoom?.id}
          />
        </div>

        {/* Chat Room Main Area */}
        <div className="flex-1">
          {selectedRoom ? (
            <ChatRoom room={selectedRoom} />
          ) : (
            <div className="flex flex-col items-center justify-center h-full text-center p-8">
              <div className="text-6xl mb-4">💬</div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                Select a Chat Room
              </h3>
              <p className="text-gray-600 dark:text-gray-400 max-w-md">
                Choose a conversation from the sidebar to start chatting. 
                You can discuss your application with our team or participate in admin discussions.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Export individual components for use elsewhere
export { ChatList, ChatRoom };
