'use client';

import { motion } from 'framer-motion';
import { ChatMessage as ChatMessageType } from '@/lib/supabase';

interface ChatMessageProps {
  message: ChatMessageType;
  isOwn: boolean;
}

export default function ChatMessage({ message, isOwn }: ChatMessageProps) {
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString([], { weekday: 'short', hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' });
    }
  };

  const getMessageTypeIcon = (type: string) => {
    switch (type) {
      case 'file':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
          </svg>
        );
      case 'system':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      default:
        return null;
    }
  };

  if (message.message_type === 'system') {
    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex justify-center my-4"
      >
        <div className="flex items-center space-x-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 px-3 py-1 rounded-full text-sm">
          {getMessageTypeIcon(message.message_type)}
          <span>{message.message}</span>
          <span className="text-xs opacity-75">{formatTime(message.created_at)}</span>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className={`flex ${isOwn ? 'justify-end' : 'justify-start'}`}
    >
      <div className={`flex max-w-xs lg:max-w-md ${isOwn ? 'flex-row-reverse' : 'flex-row'} items-end space-x-2`}>
        {/* Avatar */}
        {!isOwn && (
          <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
            {message.sender?.display_name?.[0] || message.sender?.email?.[0]?.toUpperCase() || '?'}
          </div>
        )}

        {/* Message Bubble */}
        <div className={`flex flex-col ${isOwn ? 'items-end' : 'items-start'}`}>
          {/* Sender Name (for received messages) */}
          {!isOwn && (
            <span className="text-xs text-gray-500 dark:text-gray-400 mb-1 px-3">
              {message.sender?.display_name || message.sender?.email || 'Unknown User'}
            </span>
          )}

          {/* Message Content */}
          <div
            className={`relative px-4 py-2 rounded-2xl ${
              isOwn
                ? 'bg-blue-600 text-white rounded-br-md'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white rounded-bl-md'
            }`}
          >
            {/* File Message */}
            {message.message_type === 'file' && message.file_url ? (
              <div className="flex items-center space-x-2">
                {getMessageTypeIcon(message.message_type)}
                <a
                  href={message.file_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`underline ${isOwn ? 'text-blue-100 hover:text-white' : 'text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300'}`}
                >
                  {message.message || 'Download File'}
                </a>
              </div>
            ) : (
              /* Text Message */
              <div className="whitespace-pre-wrap break-words">
                {message.message}
              </div>
            )}

            {/* Edited Indicator */}
            {message.is_edited && (
              <span className={`text-xs opacity-75 ${isOwn ? 'text-blue-100' : 'text-gray-500 dark:text-gray-400'}`}>
                (edited)
              </span>
            )}
          </div>

          {/* Timestamp */}
          <span className={`text-xs text-gray-500 dark:text-gray-400 mt-1 px-3 ${isOwn ? 'text-right' : 'text-left'}`}>
            {formatTime(message.created_at)}
          </span>
        </div>

        {/* Own Avatar */}
        {isOwn && (
          <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-br from-green-500 to-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
            {message.sender?.display_name?.[0] || message.sender?.email?.[0]?.toUpperCase() || 'Y'}
          </div>
        )}
      </div>
    </motion.div>
  );
}
