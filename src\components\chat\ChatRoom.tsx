'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { db, ChatRoom as ChatRoomType, ChatMessage, supabase } from '@/lib/supabase';
import { useAuth } from '@/lib/auth/AuthContext';
import ChatMessageComponent from './ChatMessage';
import ChatInput from './ChatInput';

interface ChatRoomProps {
  room: ChatRoomType;
  onClose?: () => void;
}

export default function ChatRoom({ room, onClose }: ChatRoomProps) {
  const { user } = useAuth();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [typingUsers, setTypingUsers] = useState<string[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  // Load messages
  useEffect(() => {
    const loadMessages = async () => {
      try {
        setLoading(true);
        const roomMessages = await db.getChatMessages(room.id);
        setMessages(roomMessages);
      } catch (error) {
        console.error('Error loading messages:', error);
        setError('Failed to load messages');
      } finally {
        setLoading(false);
      }
    };

    loadMessages();
  }, [room.id]);

  // Set up real-time subscriptions
  useEffect(() => {
    if (!user?.id) return;

    console.log('Setting up realtime subscriptions for room:', room.id);

    // Subscribe to new messages
    const messagesChannel = supabase
      .channel(`chat_messages:${room.id}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'chat_messages',
          filter: `room_id=eq.${room.id}`,
        },
        async (payload) => {
          console.log('New message received:', payload);

          // Fetch the complete message with sender info
          const completeMessages = await db.getChatMessages(room.id, 1);
          if (completeMessages.length > 0) {
            const latestMessage = completeMessages[0];
            setMessages(prev => {
              // Avoid duplicates
              if (prev.some(msg => msg.id === latestMessage.id)) {
                return prev;
              }
              return [...prev, latestMessage];
            });
          }
        }
      )
      .subscribe((status) => {
        console.log('Messages channel subscription status:', status);
        if (status !== 'SUBSCRIBED') {
          setError('Failed to connect to chat. Please refresh the page.');
        }
      });

    // Subscribe to typing indicators (using presence)
    const typingChannel = supabase
      .channel(`typing:${room.id}`)
      .on('presence', { event: 'sync' }, () => {
        const state = typingChannel.presenceState();
        const typingUserIds = Object.keys(state).filter(userId => userId !== user.id);
        setTypingUsers(typingUserIds);
      })
      .subscribe((status) => {
        console.log('Typing channel subscription status:', status);
      });

    return () => {
      console.log('Cleaning up realtime subscriptions for room:', room.id);
      supabase.removeChannel(messagesChannel);
      supabase.removeChannel(typingChannel);
    };
  }, [room.id, user?.id]);

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Update last read timestamp
  useEffect(() => {
    if (user?.id && messages.length > 0) {
      db.updateLastReadAt(room.id, user.id);
    }
  }, [room.id, user?.id, messages]);

  const handleSendMessage = async (content: string) => {
    if (!user?.id || !content.trim()) return;

    try {
      const messageData = {
        room_id: room.id,
        sender_id: user.id,
        message: content.trim(),
        message_type: 'text' as const
      };

      await db.sendChatMessage(messageData);
    } catch (error) {
      console.error('Error sending message:', error);
      setError('Failed to send message');
    }
  };

  const handleTyping = () => {
    if (!user?.id) return;

    setIsTyping(true);
    
    // Send typing indicator
    const typingChannel = supabase.channel(`typing:${room.id}`);
    typingChannel.track({ user_id: user.id, typing: true });

    // Clear previous timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Stop typing after 3 seconds
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
      typingChannel.untrack();
    }, 3000);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-white dark:bg-gray-800 rounded-lg shadow-lg">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            {room.name || 'Chat Room'}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {room.type === 'applicant_admin' ? 'Application Discussion' : 'Admin Chat'}
          </p>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
            <p className="text-red-700 dark:text-red-300 text-sm">{error}</p>
          </div>
        )}

        <AnimatePresence>
          {messages.map((message) => (
            <ChatMessageComponent
              key={message.id}
              message={message}
              isOwn={message.sender_id === user?.id}
            />
          ))}
        </AnimatePresence>

        {/* Typing Indicator */}
        {typingUsers.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400"
          >
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
            </div>
            <span>Someone is typing...</span>
          </motion.div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="border-t border-gray-200 dark:border-gray-700">
        <ChatInput
          onSendMessage={handleSendMessage}
          onTyping={handleTyping}
          disabled={loading}
        />
      </div>
    </div>
  );
}
